{"name": "astro-app", "version": "1.0.0", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "postbuild": "pagefind --site dist", "preview": "astro preview", "lint": "biome lint .", "format": "pnpm run format:code && pnpm run format:imports", "format:code": "biome format . --write && prettier -w \"**/*\" \"!**/*.{md,mdx}\" --ignore-unknown --cache", "format:imports": "biome check --formatter-enabled=false --write", "check": "astro check"}, "dependencies": {"@astrojs/mdx": "4.0.3", "@astrojs/rss": "4.0.11", "@astrojs/sitemap": "3.2.1", "@astrojs/tailwind": "5.1.4", "@shikijs/transformers": "^1.25.1", "astro": "5.1.2", "astro-icon": "^1.1.5", "astro-robots-txt": "^1.0.0", "astro-webmanifest": "^1.0.0", "cssnano": "^7.0.6", "hastscript": "^9.0.0", "mdast-util-directive": "^3.0.0", "mdast-util-to-markdown": "^2.1.2", "mdast-util-to-string": "^4.0.0", "rehype-external-links": "^3.0.0", "rehype-pretty-code": "^0.14.0", "rehype-unwrap-images": "^1.0.0", "remark-directive": "^3.0.0", "satori": "0.12.0", "satori-html": "^0.3.2", "sharp": "^0.33.5", "unified": "^11.0.5", "unist-util-visit": "^5.0.0"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@biomejs/biome": "^1.9.4", "@iconify-json/hugeicons": "^1.2.3", "@iconify-json/mdi": "^1.2.2", "@iconify-json/solar": "^1.2.2", "@pagefind/default-ui": "^1.3.0", "@resvg/resvg-js": "^2.6.2", "@tailwindcss/typography": "^0.5.15", "@types/hast": "^3.0.4", "@types/mdast": "^4.0.4", "autoprefixer": "^10.4.20", "pagefind": "^1.3.0", "prettier": "^3.4.2", "prettier-plugin-astro": "0.14.1", "prettier-plugin-tailwindcss": "^0.6.9", "reading-time": "^1.5.0", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}, "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "esbuild", "sharp"]}}