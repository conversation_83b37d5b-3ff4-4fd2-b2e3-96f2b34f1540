{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "formatter": {"indentStyle": "tab", "indentWidth": 2, "lineWidth": 100, "formatWithErrors": true, "ignore": ["*.astro"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"noSvgWithoutTitle": "off"}, "suspicious": {"noExplicitAny": "warn"}}}, "javascript": {"formatter": {"trailingCommas": "all", "semicolons": "always"}}, "vcs": {"clientKind": "git", "enabled": true, "useIgnoreFile": true}}