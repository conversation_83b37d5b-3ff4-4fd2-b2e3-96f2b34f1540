{
  "extends": "astro/tsconfigs/strictest",
  "compilerOptions": {
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "baseUrl": ".",
    "lib": [
      "es2022",
      "dom",
      "dom.iterable"
    ],
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
  },
  "include": [
    ".astro/types.d.ts",
    "**/*"
  ],
  "exclude": [
    "node_modules",
    "**/node_modules/*",
    ".vscode",
    "dist"
  ]
}