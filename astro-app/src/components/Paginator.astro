---
import type { PaginationLink } from "@/types";

interface Props {
	nextUrl?: PaginationLink;
	prevUrl?: PaginationLink;
}

const { nextUrl, prevUrl } = Astro.props;
---

{
	(prevUrl || nextUrl) && (
		<nav class="flex items-center gap-x-4 font-medium text-accent">
			{prevUrl && (
				<a class="me-auto py-2 sm:hover:text-accent-two" data-astro-prefetch href={prevUrl.url}>
					{prevUrl.srLabel && <span class="sr-only">{prevUrl.srLabel}</span>}
					{prevUrl.text ? prevUrl.text : "Previous"}
				</a>
			)}
			{nextUrl && (
				<a class="ms-auto py-2 sm:hover:text-accent-two" data-astro-prefetch href={nextUrl.url}>
					{nextUrl.srLabel && <span class="sr-only">{nextUrl.srLabel}</span>}
					{nextUrl.text ? nextUrl.text : "Next"}
				</a>
			)}
		</nav>
	)
}
