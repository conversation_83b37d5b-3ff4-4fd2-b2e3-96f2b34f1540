---
import { Image } from "astro:assets";
import { type CollectionEntry, getCollection } from "astro:content";
import FormattedDate from "@/components/FormattedDate.astro";
import { Icon } from "astro-icon/components";
import Badge from '@/components/Badge.astro';
import Separator from "../Separator.astro";

interface Props {
	content: CollectionEntry<"post">;
}

const {
	content,

} = Astro.props;

// console.log("data:", data); // Debug
// console.log("headings:", content.rendered?.metadata?.headings); // Debug
// console.log("Post frontmatter:", content.rendered?.metadata?.frontmatter); // Debug

const dateTimeOptions: Intl.DateTimeFormatOptions = {
	month: "long",
};

const postSeries = content.data.seriesId
    ? await getCollection("series")
          .then(series => series.find(s => s.id === content.data.seriesId))
          .catch(err => {
            console.error("Failed to find series:", err);
            return null;
          })
    : null;
---

<div class="md:sticky md:top-8 md:z-10 flex items-end">
	{
		postSeries ? (
			<button
				id="toggle-panel"
				class="hidden md:flex mr-2 h-8 w-8 items-center bg-accent-base/10 flex-shrink-0 justify-center rounded-lg text-accent-base hover:brightness-110"
				aria-label="Toggle Series Panel"
				aria-controls="series-panel"
			>
				{/*
				<Icon aria-hidden="true" class="h-6 w-6" focusable="false" name="hugeicons:sidebar-left" />
				*/}
				<Icon aria-hidden="true" class="flex-shrink-0 h-6 w-6" focusable="false" name="solar:notes-bold" />
			</button>
		) : null 
	}

	{
		!!(content.rendered?.metadata?.headings as unknown[] | undefined)?.length && (
			<button
				id="toggle-toc"
				class="hidden md:flex h-8 w-8 items-center flex-shrink-0 bg-accent-base/10 justify-center rounded-lg text-accent-base hover:brightness-110"
				aria-label="Table of Contents"
			>	
				<Icon aria-hidden="true" class="h-6 w-6" focusable="false" name="solar:clipboard-list-bold" />
			</button>
		)
	}

	<h1 
		class="title ml-2 md:sticky md:top-4 md:z-20 line-clamp-none md:line-clamp-1 md:max-w-[calc(100%-10rem)]"
		title={content.data.title}
		data-pagefind-body
	>
		{content.data.title}
	</h1>
</div>

<div class="flex flex-wrap items-center text-lighter text-sm mt-[1.0625rem] mx-2 mb-2">
	<span class="flex items-center h-[1.75rem]">
		<Icon aria-hidden="true" class="flex items-center h-4 w-4 me-1" focusable="false" name="hugeicons:calendar-03" />
		<FormattedDate date={content.data.publishDate} dateTimeOptions={dateTimeOptions} class="flex flex-shrink-0" />
	</span>
	<Separator type="dot" />
	<span class="flex items-center h-[1.75rem]">
		<Icon aria-hidden="true" class="flex items-center inline-block h-4 w-4 me-1" focusable="false" name="hugeicons:book-open-01" />
		{/* @ts-ignore:next-line. TODO: add reading time to collection schema? */}
		{content.rendered?.metadata?.frontmatter?.readingTime ? `${content.rendered.metadata.frontmatter.readingTime}` : "Less than one minute read"}
	</span>
	{
		content.data.updatedDate && (
			<Separator type="dot" />
			<span class="h-[1.75rem] flex items-center flex-shrink-0 rounded-lg bg-accent-two/5 text-accent-two py-1 px-2 text-sm gap-x-1">
				Updated:<FormattedDate class="flex flex-shrink-0" date={content.data.updatedDate} dateTimeOptions={dateTimeOptions} />
			</span>
		)
	}
</div>

{content.data.draft ? <span class="text-base text-red-500 ml-2">(Draft)</span> : null}

{
	content.data.coverImage && (
		<div class="mb-4 mt-2 overflow-auto rounded-lg">
			<Image
				alt={content.data.coverImage.alt}
				class="object-cover"
				fetchpriority="high"
				loading="lazy" // loading="eager"
				src={content.data.coverImage.src}
			/>
		</div>
	)
}

<p 
	class="prose max-w-none text-textColor mx-2"
	data-pagefind-body
>
	{content.data.description}
</p>

<div class="mt-4 flex flex-wrap items-center gap-2 mx-1">
	{/* Tags */}
	{
		content.data.tags?.length ? (
			<Icon aria-hidden="true" class="flex-shrink-0 inline-block h-6 w-6 text-accent-base" focusable="false" name="solar:tag-line-duotone" />	
			<>
				{content.data.tags.map((tag) => (
					<a aria-label={`View all posts with the tag: ${tag}`} href={`/tags/${tag}`}>
						<Badge variant="accent-two" title={tag} />
					</a>
				))}
			</>
		) : (
			<Icon aria-hidden="true" class="flex-shrink-0 inline-block h-6 w-6 text-lightest" focusable="false" name="solar:tag-line-duotone" />
			<span class="text-sm text-lightest">No tags</span>
		)
	}

	{/* Series */}
	{
		postSeries ? (
			<div class="flex items-center gap-2">
				<Icon aria-hidden="true" class="flex-shrink-0 inline-block h-6 w-6 text-accent-base" focusable="false" name="solar:notes-line-duotone" />
				<a 
					aria-label={`About ${postSeries.data.title} series`} 
					href={`/series/${postSeries.id}`}
					class="flex items-center gap-2 flex-wrap"
				>
					<Badge variant="accent-base" showHash={false} title={postSeries.data.title} />
				</a>
			</div>	
		) : (
			<div class="flex items-center gap-2">
				<Icon aria-hidden="true" class="flex-shrink-0 inline-block h-6 w-6 text-lightest" focusable="false" name="solar:notes-line-duotone" />
				<span class="text-sm text-lightest">Not in series</span>
			</div>
		)
	}
</div>