---
import type { CollectionEntry } from "astro:content";
import FormattedDate from "@/components/FormattedDate.astro";
import type { HTMLTag, Polymorphic } from "astro/types";

type Props<Tag extends HTMLTag> = Polymorphic<{ as: Tag }> & {
	post: CollectionEntry<"post">;
	withDesc?: boolean;
};

const { as: Tag = "div", post, withDesc = false } = Astro.props;
---

<div class={withDesc ? "flex flex-col" : "flex flex-col grow sm:flex-row sm:items-center sm:justify-between"}>
	{!withDesc ? (
		<>
			<FormattedDate
				class="shrink-0 text-lighter text-sm sm:order-2 sm:text-right"
				date={post.data.publishDate}
				dateTimeOptions={{
					// hour: "2-digit",
					// minute: "2-digit",
					year: "numeric",
					month: "long",
					day: "2-digit",
				}}
			/>
			<Tag class="citrus-link font-medium text-accent-base sm:order-1 sm:flex-gro md:line-clamp-1">
				<a data-astro-prefetch href={`/posts/${post.id}/`}>
					{post.data.draft && <span class="text-red-500">(Draft) </span>}
					{post.data.title}
				</a>
			</Tag>
		</>
	) : (
		<>
			<FormattedDate 
				class="text-sm shrink-0 text-lighter" 
				date={post.data.publishDate}
				dateTimeOptions={{
					// hour: "2-digit",
					// minute: "2-digit",
					year: "numeric",
					month: "long",
					day: "2-digit",
				}}
			/>
			<Tag class="citrus-link font-medium text-accent-base mt-2.5">
				<a data-astro-prefetch href={`/posts/${post.id}/`}>
					{post.data.title}
				</a>
			</Tag>
			<p class="mt-0.5 line-clamp-2">{post.data.description}</p>
		</>
	)}
</div>
