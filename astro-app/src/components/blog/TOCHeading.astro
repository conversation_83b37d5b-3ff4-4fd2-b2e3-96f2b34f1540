---
import type { TocItem } from "@/utils/generateToc";

interface Props {
	heading: TocItem;
}

const {
	heading: { children, depth, slug, text },
} = Astro.props;
---

<li class="">
	<a
		aria-label={`Scroll to section: ${text}`}
		class="text-light mt-1 line-clamp-2 break-words [padding-left:1ch] [text-indent:-1ch] before:text-accent-two before:content-['#'] hover:text-accent-two"
		href={`#${slug}`}
		>{text}
	</a>
	{!!children.length && (
		<ul class="ms-2">
			{children.map((subheading) => (
				<Astro.self heading={subheading} />
			))}
		</ul>
	)}
</li>