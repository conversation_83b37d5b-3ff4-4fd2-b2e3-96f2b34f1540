---
import { type CollectionEntry, render } from "astro:content";
import FormattedDate from "@/components/FormattedDate.astro";
import type { HTMLTag, Polymorphic } from "astro/types";

type Props<Tag extends HTMLTag> = Polymorphic<{ as: Tag }> & {
	note: CollectionEntry<"note">;
	isPreview?: boolean | undefined;
};

const { as: Tag = "div", note, isPreview = false } = Astro.props;
const { Content } = await render(note);
---

<article
	class:list={[isPreview && "inline-grid w-full rounded-lg bg-color-75 px-4 md:px-8 py-2 md:py-4"]}
	data-pagefind-body={isPreview ? false : true}
>
	<Tag class="flex items-end title md:sticky md:top-8 md:z-10" class:list={{ "text-base": isPreview }}>
		{
			isPreview ? (
				<a class="citrus-link" href={`/notes/${note.id}/`}>
					{note.data.title}
				</a>
			) : (
				<>{note.data.title}</>
			)
		}
	</Tag>
	<div 
		class="flex items-end h-6 text-sm text-lighter"
		class:list={{ "mt-4": !isPreview }}
	>
		
		<FormattedDate
			dateTimeOptions={{
				hour: "2-digit",
				minute: "2-digit",
				year: "numeric",
				month: "long",
				day: "2-digit",
			}}
			date={note.data.publishDate}
		/>
	</div>
	<div
		class="prose prose-citrus mt-4 max-w-none [&>p:last-of-type]:mb-0"
		class:list={{
			"line-clamp-4": isPreview,
			"[&>blockquote]:line-clamp-4 [&>blockquote]:mb-0": isPreview,
			"[&>blockquote:not(:first-of-type)]:hidden": isPreview,
			// "[&>p]:line-clamp-4": isPreview,
			// "[&>p:not(:first-of-type)]:hidden": isPreview,
		}} 
	>
		<Content />
	</div>
</article>
