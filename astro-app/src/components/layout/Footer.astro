---
import { menuLinks, siteConfig } from "@/site.config";

const year = new Date().getFullYear();
---

<footer
	class="semibold mt-auto flex w-full flex-col items-center justify-center gap-y-2 pb-4 pt-8 text-center align-top text-accent sm:flex-row sm:justify-between sm:text-sm"
>
	<div class="me-0 font-semibold sm:me-4">
		&copy; {siteConfig.author}
		{year}.<span class="inline-block">&nbsp;🚀&nbsp;Astro Citrus</span>
	</div>
	<nav
		aria-label="More on this site"
		class="flex justify-between space-x-4 font-medium text-light md:w-[14rem] w-auto"
	>
		{
			menuLinks.map((link) => (
				<a class="underline-offset-2 hover:text-accent hover:underline" href={link.path}>
					{link.title}
				</a>
			))
		}
	</nav>
</footer>
