---
import Search from "@/components/Search.astro";
import ThemeToggle from "@/components/ThemeToggle.astro";
import { siteConfig, menuLinks } from "@/site.config";
import { Icon } from "astro-icon/components";
---

<header
	id="main-header"
	class="fixed px-4 md:px-0 left-0 z-20 flex items-center md:relative top-0 h-16 w-full bg-bgColor md:bg-transparent overflow-hidden"
>	
	<!-- Background 
			TODO: This approach is not optimal and requires improvements. 
  			- Too many absolutely positioned elements can affect performance. 
  		-->	
	<div class="md:hidden absolute top-0 left-1/2 -ml-[50vw] w-screen min-h-screen pointer-events-none blur-2xl">
		<div class="absolute top-[-90%] right-[25%] w-[75%] h-full bg-gradient-to-b from-blue-300 via-pink-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
		<div class="absolute top-[-90%] left-[25%] w-[75%] h-full bg-gradient-to-b from-blue-300 via-pink-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
		<div class="absolute top-[-85%] right-[25%] w-[55%] h-full bg-gradient-to-b from-purple-300 via-blue-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
		<div class="absolute top-[-85%] left-[25%] w-[55%] h-full bg-gradient-to-b from-indigo-300 via-orange-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
		<div class="absolute top-[-75%] left-[-25%] w-[65%] h-full bg-gradient-to-b from-blue-300 via-pink-300 to-transparent rounded-full opacity-30 dark:opacity-5"></div>
		<div class="absolute top-[-75%] right-[-25%] w-[65%] h-full bg-gradient-to-b from-purple-300 via-blue-300 to-transparent rounded-full opacity-30 dark:opacity-5"></div>
		<div class="absolute top-[-85%] left-[-30%] w-[85%] h-full bg-gradient-to-b from-indigo-300 via-orange-300 to-transparent rounded-full opacity-60 dark:opacity-5"></div>
		<div class="absolute top-[-85%] right-[-30%] w-[85%] h-full bg-gradient-to-b from-orange-300 via-indigo-300 to-transparent rounded-full opacity-60 dark:opacity-5"></div>
	</div>
	
	<div class="w-full justify-between sm:flex-col">
		<div class="flex items-center gap-x-2">
			<a
				aria-label={siteConfig.title}
				aria-current={Astro.url.pathname === "/" ? "page" : false}
				class="group flex h-8 items-center hover:filter-none sm:relative"
				href="/"
			>	
				<!-- Logo -->
				<!--
				<div class="pt-1.5">
					<svg class="inline-block size-8 fill-current text-accent-base drop-shadow-[0px_2.5px_2.5px_rgba(0,0,0,0.35)]">
						<use href="/brand.svg#brand"></use>
					</svg>
				</div>
				-->
				<div title={siteConfig.title}>
					<img src="/logo.svg#layer1" alt={siteConfig.title} class="inline-block size-8 drop-shadow-[0px_2.5px_2.5px_rgba(0,0,0,0.35)]" />
				</div>
				
				<strong class="'max-[320px]:hidden' bg-clip-text text-transparent bg-gradient-to-r from-accent-one to-accent-two hidden xs:block z-10 mb-0.5 ms-2 text-2xl group-hover:text-accent-one">
					{siteConfig.title}
				</strong>
			</a>
			<nav
				aria-label="Main menu"
				class="top-20 text-sm mx-auto ml-4 ml-auto hidden flex-col items-end justify-center gap-x-4 rounded-md bg-bgColor font-medium text-accent-two shadow backdrop-blur group-[.menu-open]:z-50 group-[.menu-open]:flex sm:static sm:z-auto sm:flex-row sm:items-center sm:rounded-none sm:bg-transparent sm:shadow-none sm:backdrop-blur-none md:flex"
				id="main-navigation-menu"
			>	
				<!--				
				<a
					aria-current={Astro.url.pathname === "/" ? "page" : false}
					class="flex gap-x-1 h-8 items-center justify-center rounded-lg underline-offset-2 hover:underline"
					href="/"
				>	
					<Icon
						class="size-4 drop-shadow-[0px_1.5px_1.5px_rgba(0,0,0,0.175)]"
						name="solar:home-2-bold"
					/>
					Home
				</a>
				<a
					aria-current={Astro.url.pathname === "/" ? "page" : false}
					class="flex gap-x-1 h-8 items-center justify-center rounded-lg underline-offset-2 hover:underline"
					href="/"
				>	
					<Icon
						class="size-4 drop-shadow-[0px_1.5px_1.5px_rgba(0,0,0,0.175)]"
						name="solar:archive-bold"
					/>
					Blog
				</a>
				-->

				<!-- Ссылки меню -->
				{
					menuLinks.map((link) => (
						<a
							aria-current={Astro.url.pathname === link.path ? "page" : false}
							class="underline-offset-2 hover:underline"
							data-astro-prefetch
							href={link.path}
						>
							{link.title}
						</a>
					))
				}
				
				<a
					class="flex h-8 items-center rounded-lg bg-accent-base/5 px-4 text-accent-base underline-offset-2 hover:bg-accent-base/10"
					data-astro-prefetch
					href="/posts/citrus-docs/intro/"
				>
					Docs
				</a>
				
				<!-- Dropdown menu button for large screens. Needs improvement. -->
				<!--
				<button
					aria-expanded="false"
					aria-haspopup="menu"
					aria-label="Open main menu"
					class="hidden md:flex group text-sm relative h-8 w-16 font-medium items-center justify-center px-4 rounded-lg bg-accent-base/5 text-accent-base hover:bg-accent-base/10"
					id="toggle-navigation-menu"
					type="button"
				>
					Menu
				</button>
				-->
			</nav>
			
			<!-- Dropdown menu for large screens. Needs improvement. -->
			<!--
			<div id="menu" class="absolute left-0 right-0 w-fit ml-auto top-16 z-10 hidden" aria-hidden="true">
				<div
					id="menu-body"
					class="fixed bg-bgColor rounded-lg -ml-56 w-56"
				>
					<nav
						aria-label="Main menu"
						class="px-4 py-4 rounded-lg border border-special-lighter bg-special-light shadow-[0px_10px_25px_rgba(0,0,0,0.15)] text-sm flex flex-col gap-y-2 font-medium"
						id="main-navigation-menu"
					>
						{
							menuLinks.map((link) => (
								<a
									aria-current={Astro.url.pathname === link.path ? "page" : false}
									class="text-accent-two underline-offset-2 hover:underline rounded-lg h-8 gap-x-1 px-2 flex justify-center items-center"
									data-astro-prefetch
									href={link.path}
								>
									{link.title}
								</a>
							))
						}
						<a
							class="flex h-8 items-center justify-center rounded-lg bg-accent-base/5 hover:bg-accent-base/10 px-4 text-accent-base"
							data-astro-prefetch
							href="/posts/citrus-docs/intro/"
						>
							Docs
						</a>
					</nav>
				</div>
			</div>
			-->

			<div class="ml-auto w-fit">
				<div id="buttons-panel" class="top-4 md:top-8 -ml-[4.5rem] flex space-x-2">
					<Search />
					<ThemeToggle />
				</div>
			</div>	
			
			<mobile-button
				aria-expanded="false"
				aria-haspopup="menu"
				aria-label="Open main menu"
				class="group relative h-8 w-8 rounded-lg bg-color-100 hover:bg-accent-base/10 text-accent-base md:invisible md:hidden"
				id="toggle-nav-menu-mobile"
				type="button"
			>
				<Icon
					id="open-nav-menu-icon"
					aria-hidden="true"
					class="absolute start-1/2 top-1/2 h-6 w-6 -translate-x-1/2 -translate-y-1/2 scale-100 opacity-100 transition-all"
					focusable="false"
					name="hugeicons:menu-01"
				/>
				<Icon
					id="close-nav-menu-icon"
					aria-hidden="true"
					class="absolute start-1/2 top-1/2 h-6 w-6 -translate-x-1/2 -translate-y-1/2 scale-0 opacity-0 transition-all"
					focusable="false"
					name="hugeicons:cancel-01"
				/>
			</mobile-button>
		</div>
	</div>
</header>

<div id="drawer" class="fixed inset-0 z-10 hidden" aria-hidden="true">
	<div
		id="drawer-body"
		class="absolute inset-0 -translate-y-full transform bg-bgColor transition-all duration-300 ease-in-out"
	>
		<!-- Background 
			TODO: This approach is not optimal and requires improvements. 
  			- Too many absolutely positioned elements can affect performance. 
  		-->		
		<div class="fixed top-0 left-1/2 -ml-[50vw] w-screen pointer-events-none min-h-screen overflow-x-hidden overflow-y-visible blur-2xl">
				<!--
				<div class="fixed blur-xl top-0 left-0 w-full h-16 md:h-20 bg-gradient-to-b from-indigo-300 via-purple-300 to-transparent opacity-10 dark:opacity-5"></div>
				-->
				<div class="absolute top-[-90%] right-[25%] w-[75%] h-full bg-gradient-to-b from-blue-300 via-pink-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
				<div class="absolute top-[-90%] left-[25%] w-[75%] h-full bg-gradient-to-b from-blue-300 via-pink-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
				<div class="absolute top-[-85%] right-[25%] w-[55%] h-full bg-gradient-to-b from-purple-300 via-blue-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
				<div class="absolute top-[-85%] left-[25%] w-[55%] h-full bg-gradient-to-b from-indigo-300 via-orange-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
				<div class="absolute top-[-75%] left-[-25%] w-[65%] h-full bg-gradient-to-b from-blue-300 via-pink-300 to-transparent rounded-full opacity-30 dark:opacity-5"></div>
				<div class="absolute top-[-75%] right-[-25%] w-[65%] h-full bg-gradient-to-b from-purple-300 via-blue-300 to-transparent rounded-full opacity-30 dark:opacity-5"></div>
				<div class="absolute top-[-85%] left-[-30%] w-[85%] h-full bg-gradient-to-b from-indigo-300 via-orange-300 to-transparent rounded-full opacity-60 dark:opacity-5"></div>
				<div class="absolute top-[-85%] right-[-30%] w-[85%] h-full bg-gradient-to-b from-orange-300 via-indigo-300 to-transparent rounded-full opacity-60 dark:opacity-5"></div>
		
		</div>	
		
		<nav
			aria-label="Mobile navigation menu"
			class="text-lg absolute inset-0 flex flex-col items-center justify-center gap-y-4 text-center font-medium text-accent-two"
			id="nav-menu-mobile"
		>
			<!-- Ссылки меню -->
			{
				menuLinks.map((link) => (
					<a
						aria-current={Astro.url.pathname === link.path ? "page" : false}
						class="underline-offset-2 hover:underline"
						data-astro-prefetch
						href={link.path}
					>
						{link.title}
					</a>
				))
			}
			<a
				class="flex h-8 items-center rounded-lg bg-accent-base/5 px-4 text-accent-base underline-offset-2 hover:bg-accent-base/10"
				data-astro-prefetch
				href="/series/citrus-docs"
			>
				Docs
			</a>
		</nav>
	</div>
</div>

<script>
	document.addEventListener("DOMContentLoaded", () => {
		const toggleNavMenuMobileButton = document.getElementById("toggle-nav-menu-mobile");
		const openMenuIcon = document.getElementById("open-nav-menu-icon");
		const closeMenuIcon = document.getElementById("close-nav-menu-icon");
		const drawer = document.getElementById("drawer");

		// Проверяем существование элементов
		if (!toggleNavMenuMobileButton || !openMenuIcon || !closeMenuIcon || !drawer) {
			console.error("One or more required elements are missing in the DOM.");
			return;
		}

		const drawerBody = document.getElementById("drawer-body");
		if (!drawerBody) {
			console.error("Drawer body element is missing in the DOM.");
			return;
		}

		toggleNavMenuMobileButton.addEventListener("click", () => {
			const isOpen = toggleNavMenuMobileButton.getAttribute("aria-expanded") === "true";

			if (isOpen) {
				// Закрываем меню
				drawerBody.classList.add("opacity-0", "-translate-y-full");
				drawerBody.classList.remove("translate-y-0");

				// Убираем после анимации
				setTimeout(() => {
					drawer.classList.add("hidden");
				}, 300);

				// Меняем иконки
				openMenuIcon.classList.add("scale-100", "opacity-100");
				closeMenuIcon.classList.add("scale-0", "opacity-0");
				closeMenuIcon.classList.remove("scale-100", "opacity-100");
			} else {
				// Показываем меню
				drawer.classList.remove("hidden");
				drawerBody.classList.add("translate-y-0");
				drawerBody.classList.remove("opacity-0", "-translate-y-full");

				// Меняем иконки
				openMenuIcon.classList.add("scale-0", "opacity-0");
				closeMenuIcon.classList.add("scale-100", "opacity-100");
				openMenuIcon.classList.remove("scale-100", "opacity-100");
			}

			// Обновляем состояние кнопки
			toggleNavMenuMobileButton.setAttribute("aria-expanded", (!isOpen).toString());
		});
	});
</script>



