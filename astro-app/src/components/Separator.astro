---
export interface SeparatorProps {
	type?: "horizontal" | "vertical" | "dot"; // Тип разделителя
	className?: string; // Дополнительные классы
}

const { type = "horizontal", className = "" } = Astro.props;

// Классы для разделителей в зависимости от типа
const separatorClasses = {
	base: "flex-shrink-0 bg-lighter mx-2", // Общие стили
	types: {
		horizontal: "h-[1px] w-full", // Горизонтальный разделитель
		vertical: "h-full w-[1px]", // Вертикальный разделитель
		dot: "w-1.5 h-1.5 rounded-full", // Кружок для dot
	} as const, // Указываем, что типы здесь конкретные строки
};

// Убедитесь, что type - это один из допустимых типов
const typeClass = separatorClasses.types[type as keyof typeof separatorClasses.types];
---

{
	type === "dot" ? (
		<span class={`${separatorClasses.base} ${typeClass} ${className}`} />
	) : (
		<span
			role="separator"
			aria-orientation={type === "horizontal" ? "horizontal" : "vertical"}
			class={`${separatorClasses.base} ${typeClass} ${className}`}
		/>
	)
}
