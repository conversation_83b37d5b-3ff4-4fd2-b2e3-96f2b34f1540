---
import BaseHead from "@/components/BaseHead.astro";
import SkipLink from "@/components/SkipLink.astro";
import ThemeProvider from "@/components/ThemeProvider.astro";
import Header from "@/components/layout/Header.astro";
import Footer from "@/components/layout/Footer.astro";
import { siteConfig } from "@/site.config";
import type { SiteMeta } from "@/types";

interface Props {
	meta: SiteMeta;
}

const {
	meta: { articleDate, description = siteConfig.description, ogImage, title },
} = Astro.props;
---

<html
	class="overflow-x-hidden grid scroll-pt-20 scroll-smooth font-sans text-textColor text-xl md:text-base antialiased"
	lang={siteConfig.lang}
>
	<head>
		<BaseHead articleDate={articleDate} description={description} ogImage={ogImage} title={title} />
	</head>
	<body class="min-w-4xl relative m-auto min-h-screen w-full max-w-6xl grow bg-bgColor">
		<ThemeProvider />
		<SkipLink />

		<!-- Background 
			TODO: This approach is not optimal and requires improvements. 
  			- Too many absolutely positioned elements can affect performance. 
  		-->	
		<div class="fixed top-0 left-1/2 -ml-[50vw] min-h-screen w-screen pointer-events-none blur-2xl">
			<div class="absolute top-[-90%] right-[25%] w-[75%] h-full bg-gradient-to-b from-blue-300 via-pink-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
			<div class="absolute top-[-90%] left-[25%] w-[75%] h-full bg-gradient-to-b from-blue-300 via-pink-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
			<div class="absolute top-[-85%] right-[25%] w-[55%] h-full bg-gradient-to-b from-purple-300 via-blue-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
			<div class="absolute top-[-85%] left-[25%] w-[55%] h-full bg-gradient-to-b from-indigo-300 via-orange-300 to-transparent rounded-full opacity-40 dark:opacity-5"></div>
			<div class="absolute top-[-75%] left-[-25%] w-[65%] h-full bg-gradient-to-b from-blue-300 via-pink-300 to-transparent rounded-full opacity-30 dark:opacity-5"></div>
			<div class="absolute top-[-75%] right-[-25%] w-[65%] h-full bg-gradient-to-b from-purple-300 via-blue-300 to-transparent rounded-full opacity-30 dark:opacity-5"></div>
			<div class="absolute top-[-85%] left-[-30%] w-[85%] h-full bg-gradient-to-b from-indigo-300 via-orange-300 to-transparent rounded-full opacity-60 dark:opacity-5"></div>
			<div class="absolute top-[-85%] right-[-30%] w-[85%] h-full bg-gradient-to-b from-orange-300 via-indigo-300 to-transparent rounded-full opacity-60 dark:opacity-5"></div>
		</div>
	
		<div class="relative flex min-h-screen w-full">
			<!-- Sidebar for Docs Series -->
			<slot name="sidebar" />
			<!-- Main container for the page content -->
			<div id="container" class="min-w-3xl relative m-auto max-w-4xl grow">
				<div class="m-auto grid min-h-screen md:grid-rows-[auto_auto_1fr] px-4 md:px-8 md:pt-4">
					<Header />
					<main id="main" class="relative flex-grow mt-32 md:mt-[3.5rem]">
						<slot />
					</main>
					<Footer />
				</div>
			</div>
		</div>
	</body>
</html>
