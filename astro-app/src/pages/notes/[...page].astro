---
import { type CollectionEntry, getCollection } from "astro:content";
import Pagination from "@/components/Paginator.astro";
import Note from "@/components/note/Note.astro";
import PageLayout from "@/layouts/Base.astro";
import { collectionDateSort } from "@/utils/date";
import type { GetStaticPaths, Page } from "astro";
import { Icon } from "astro-icon/components";

export const getStaticPaths = (async ({ paginate }) => {
	const MAX_NOTES_PER_PAGE = 5;
	const allNotes = await getCollection("note");
	return paginate(allNotes.sort(collectionDateSort), { pageSize: MAX_NOTES_PER_PAGE });
}) satisfies GetStaticPaths;

interface Props {
	page: Page<CollectionEntry<"note">>;
	uniqueTags: string[];
}

const { page } = Astro.props;

const meta = {
	description: "Read my collection of notes",
	title: "Notes",
};

const paginationProps = {
	...(page.url.prev && {
		prevUrl: {
			text: "← Previous Page",
			url: page.url.prev,
		},
	}),
	...(page.url.next && {
		nextUrl: {
			text: "Next Page →",
			url: page.url.next,
		},
	}),
};
---

<PageLayout meta={meta}>
	<section>
		<h1 class="title mb-6 flex items-center gap-2">
			Notes
			<a class="text-accent-two" href="/notes/rss.xml" target="_blank">
				<span class="sr-only">RSS feed</span>
				<Icon aria-hidden="true" class="h-6 w-6" focusable="false" name="mdi:rss" />
			</a>
		</h1>
		<div class="grid grid-cols-1 gap-8 md:grid-cols-2">
			{
				page.data.map((note) => (
					<div>
						<Note note={note} as="h4" isPreview />
					</div>
				))
			}
		</div>

		<Pagination {...paginationProps} />
	</section>
</PageLayout>
