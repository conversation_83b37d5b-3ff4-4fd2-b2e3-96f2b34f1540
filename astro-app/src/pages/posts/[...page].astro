---
import type { CollectionEntry } from "astro:content";
import Pagination from "@/components/Paginator.astro";
import PostPreview from "@/components/blog/PostPreview.astro";
import { getAllPosts, getUniqueTags, groupPostsByYear } from "@/data/post";
import PageLayout from "@/layouts/Base.astro";
import { collectionDateSort } from "@/utils/date";
import type { GetStaticPaths, Page } from "astro";
import { Icon } from "astro-icon/components";
import Badge from "@/components/Badge.astro";

export const getStaticPaths = (async ({ paginate }) => {
	const MAX_POSTS_PER_PAGE = 5;
	const MAX_TAGS = 20;
	const allPosts = await getAllPosts();
	const uniqueTags = getUniqueTags(allPosts).slice(0, MAX_TAGS);
	return paginate(allPosts.sort(collectionDateSort), {
		pageSize: MAX_POSTS_PER_PAGE,
		props: { uniqueTags },
	});
}) satisfies GetStaticPaths;

interface Props {
	page: Page<CollectionEntry<"post">>;
	uniqueTags: string[];
}

const { page, uniqueTags } = Astro.props;

const meta = {
	description: "Read my collection of posts and the things that interest me",
	title: "Posts",
};

const paginationProps = {
	...(page.url.prev && {
		prevUrl: {
			text: "← Previous Page",
			url: page.url.prev,
		},
	}),
	...(page.url.next && {
		nextUrl: {
			text: "Next Page →",
			url: page.url.next,
		},
	}),
};

const groupedByYear = groupPostsByYear(page.data);
const descYearKeys = Object.keys(groupedByYear).sort((a, b) => +b - +a);
---

<PageLayout meta={meta}>
	<h1 class="title mb-6 flex items-center gap-2">
		Posts
		<a class="text-accent-two" href="/rss.xml" target="_blank">
			<span class="sr-only">RSS feed</span>
			<Icon aria-hidden="true" class="h-6 w-6" focusable="false" name="mdi:rss" />
		</a>
	</h1>
	<div class="grid gap-y-8 sm:grid-cols-[1fr_auto] sm:gap-x-8">
		<section aria-label="Blog post list" class="grow">
			{
				descYearKeys.map((yearKey) => (
					<>
						<h4 class="title">{yearKey}</h4>
						<ul class="mb-8 mt-4 space-y-8 text-start">
							{groupedByYear[yearKey]?.map((p) => (
								<li class="grid gap-2 sm:grid-cols-[auto_1fr] sm:[&_q]:col-start-2">
									<PostPreview post={p} withDesc={true} />
								</li>
							))}
						</ul>
					</>
				))
			}
		</section>
		{
			!!uniqueTags.length && (
				<aside class="md:min-w-[14rem] md:max-w-[14rem] md:basis-[14rem]">
					<h4 class="title mb-4 flex gap-2">
						Tags
						{/*
						<Icon aria-hidden="true" class="mb-1 h-6 w-6" focusable="false" name="hugeicons:tags" />
						*/}
					</h4>
					<ul class="flex flex-wrap gap-2">
						{uniqueTags.map((tag) => (
							<li>
								<a aria-label={`View all posts with the tag: ${tag}`} href={`/tags/${tag}`}>
									<Badge variant="muted" title={tag} />
								</a>
							</li>
						))}

						<span class="text-base ms-auto inline-flex items-center h-6 sm:text-end">
							<a
								aria-label="View all blog categories"
								class="font-medium text-accent sm:hover:text-accent-two"
								href="/tags/"
							>
								View all →
							</a>
						</span>	
					</ul>
					{/*
					<span class="mt-4 block sm:text-end">
						<a
							aria-label="View all blog categories"
							class="font-medium text-accent sm:hover:text-accent-two"
							href="/tags/"
						>
							View all →
						</a>
					</span>
					*/}
				</aside>
			)
		}
	</div>
	<Pagination {...paginationProps} />
</PageLayout>
