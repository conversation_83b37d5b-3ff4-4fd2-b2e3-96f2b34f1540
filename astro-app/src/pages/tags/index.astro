---
import Badge from "@/components/Badge.astro";
import { getAllPosts, getUniqueTagsWithCount } from "@/data/post";
import PageLayout from "@/layouts/Base.astro";

const allPosts = await getAllPosts();
const allTags = getUniqueTagsWithCount(allPosts);

const meta = {
	description: "A list of all the topics I've written about in my posts",
	title: "All Tags",
};
---

<PageLayout meta={meta}>
	<h1 class="title mb-6">Tags</h1>
	<!--
	<ul class="space-y-4">
		{
			allTags.map(([tag, val]) => (
				<li class="flex items-center gap-x-2">
					<a
						class="citrus-link inline-block"
						data-astro-prefetch
						href={`/tags/${tag}/`}
						title={`View posts with the tag: ${tag}`}
					>
						&#35;{tag}
					</a>
					<a aria-label={`View all posts with the tag: ${tag}`} href={`/tags/${tag}`}>
						<Badge variant="accent-two" title={tag}>
							{tag}
						</Badge>
					</a>
					<span class="inline-block">
						- {val} Post{val > 1 && "s"}
					</span>
				</li>
			))
		}
	</ul>
	-->
	<div class="flex flex-wrap items-center gap-2">
		{
			allTags.map(([tag, val]) => (
			<div class="flex items-center gap-x-2">
				<a aria-label={`View all posts with the tag: ${tag}`} href={`/tags/${tag}`}>
				<Badge variant="muted" title={tag}>
					<span class="text-xs font-normal">
						&nbsp;{val} post{val > 1 && "s"}
					</span>
				</Badge>
				</a>
			</div>
			))
		}
	</div>
</PageLayout>
