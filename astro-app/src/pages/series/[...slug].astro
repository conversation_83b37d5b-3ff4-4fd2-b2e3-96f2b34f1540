---
import { getCollection, type CollectionEntry, render } from "astro:content";

import SeriesLayout from "@/layouts/Series.astro";
import type { GetStaticPaths, InferGetStaticPropsType } from "astro";

// If you're using an adaptor in SSR mode, getStaticPaths wont work -> https://docs.astro.build/en/guides/routing/#modifying-the-slug-example-for-ssr
export const getStaticPaths = (async () => {
	const allSeries = await getCollection("series");
	return allSeries.map((series) => ({
		params: { slug: series.id },
		props: { series },
	}));
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths>;

const { series } = Astro.props;
const { Content } = await render(series);
---

<SeriesLayout series={series}>
	<Content />
</SeriesLayout>
