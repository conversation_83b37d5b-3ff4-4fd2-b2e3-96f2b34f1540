---
import { type CollectionEntry, getCollection } from "astro:content";
import SocialList from "@/components/SocialList.astro";
import PostPreview from "@/components/blog/PostPreview.astro";
import Note from "@/components/note/Note.astro";
import { getAllPosts } from "@/data/post";
import PageLayout from "@/layouts/Base.astro";
import { collectionDateSort } from "@/utils/date";

// Posts
const MAX_POSTS = 10;
const allPosts = await getAllPosts();
const allPostsByDate = allPosts
	.sort(collectionDateSort)
	.slice(0, MAX_POSTS) as CollectionEntry<"post">[];

// Notes
const MAX_NOTES = 2;
const allNotes = await getCollection("note");
const latestNotes = allNotes.sort(collectionDateSort).slice(0, MAX_NOTES);
---

<PageLayout meta={{ title: "Home" }}>
	<!-- Hero section -->
	<!-- Background blur -->
	<div class="absolute top-0 left-1/2 md:top-[-15%] -ml-[50vw] min-h-screen w-screen pointer-events-none blur-3xl opacity-50 overflow-x-hidden">
		<div class="absolute top-[10%] right-[-40%] w-[65%] h-full bg-gradient-to-b from-purple-300 via-blue-300 to-transparent rounded-full opacity-30 dark:opacity-10"></div>
		<div class="absolute top-[10%] left-[-40%] w-[65%] h-full bg-gradient-to-b from-blue-300 via-pink-300 to-transparent rounded-full opacity-30 dark:opacity-10"></div>
		<div class="absolute top-[-20%] left-[-50%] w-[85%] h-full bg-gradient-to-b from-indigo-300 via-orange-300 to-transparent rounded-full opacity-60 dark:opacity-10"></div>
		<div class="absolute top-[-20%] right-[-50%] w-[85%] h-full bg-gradient-to-b from-orange-300 via-indigo-300 to-transparent rounded-full opacity-60 dark:opacity-10"></div>
	</div>
	<section class="max-w-xl mx-auto relative flex items-center justify-center h-screen -mt-24">
		<div class="w-full text-center">
			<span class="title text-3xl bg-gradient-to-r from-accent-two/85 via-accent-one/85 to-accent-two/85 dark:from-accent-two dark:via-accent-one dark:to-accent-two bg-clip-text text-transparent">
				Introducing Astro Citrus!
			</span>
			<p class="mt-4 mb-4 text-lg font-medium">
				Hi, I’m an Astro theme for building websites or blogs. To customize, add posts, or make it yours, click the GitHub icon below to visit my repo.
			</p>
			<div class="flex justify-center mb-4">
				<SocialList />
			</div>
			<div class="flex justify-center space-x-4 mt-4">
				<a href="/posts/" class="relative flex items-center justify-center h-8 px-4 rounded-lg shadow-lg hover:brightness-110 transition-all bg-gradient-to-r from-accent-one to-accent-two">
					<span class="text-bgColor font-semibold">
						Read Blog
					</span>
				</a>

				<a href="/notes/wake-up/" class="relative flex items-center justify-center h-8 px-4 rounded-lg shadow-lg bg-special-lighter hover:brightness-110 hover:bg-special-lightest">
					<span class="bg-clip-text text-transparent font-semibold bg-gradient-to-r from-accent-one to-accent-two">
						Wake up
					</span>
				</a>
			</div>
		</div>
	</section>

	<!-- Posts Section -->
	<section aria-label="Blog post list" class="'mt-[-100vh] pt-[100vh]'">
		<h2 class="title mb-6 text-xl text-accent-two">
			<a href="/posts/">Posts</a>
		</h2>
		<ul class="space-y-4 md:space-y-2" role="list">
			{
				allPostsByDate.map((p) => (
					<li class="gap-2 sm:grid-cols-[auto_1fr]">
						<PostPreview post={p} />
					</li>
				))
			}
		</ul>
	</section>

	<!-- Notes Section -->
	{
		latestNotes.length > 0 && (
			<section class="mt-12">
				<h2 class="title mb-6 text-accent-two">
					<a href="/notes/">Notes</a>
				</h2>
				<div class="grid grid-cols-1 gap-8 sm:grid-cols-2">
					{
						latestNotes.map((note) => (
							<div>
								<Note note={note} as="h4" isPreview />
							</div>
						))
					}
				</div>
			</section>
		)
	}

	<!-- Debug -->
	<!--
	<div class="flex justify-between flex-wrap gap-y-1.5 mt-6 text-sm font-medium">
		<div class="text-center content-center h-10 w-10 bg-color-50"><span class="text-textColor">50</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-75"><span class="text-textColor">75</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-100"><span class="text-textColor">100</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-150"><span class="text-textColor">150</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-200"><span class="text-textColor">200</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-250"><span class="text-textColor">250</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-300"><span class="text-textColor">300</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-350"><span class="text-textColor">350</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-400">
			<span class="text-textColor">400</span>
			<span class="text-bgColor">400</span>
		</div>
		<div class="text-center content-center h-10 w-10 bg-color-450">
			<span class="text-textColor">450</span>
			<span class="text-bgColor">450</span>
		</div>
		<div class="text-center content-center h-10 w-10 bg-color-500">
			<span class="text-textColor">500</span>
			<span class="text-bgColor">500</span>
		</div>
		<div class="text-center content-center h-10 w-10 bg-color-550"><span class="text-bgColor">550</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-600"><span class="text-bgColor">600</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-650"><span class="text-bgColor">650</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-700"><span class="text-bgColor">700</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-750"><span class="text-bgColor">750</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-800"><span class="text-bgColor">800</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-850"><span class="text-bgColor">850</span></div>
		<div class="text-center content-center h-10 w-10 bg-color-900"><span class="text-bgColor">900</span></div>
	</div>
	-->
</PageLayout>

