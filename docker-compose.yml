services:
  # Create a fresh Astro project in the 'astro-app' directory, based on the artemkutsan/astro-citrus template
  astro-init:
    # user: "${UID:-1000}:${GID:-1000}"
    image: node:latest
    volumes:
      - .:/app
    working_dir: /app
    command: sh ./init.sh

  # Runs the astro project, located in the 'astro-app' directory
  astro-run:
    # user: "${UID:-1000}:${GID:-1000}"
    image: node:latest
    volumes:
      - ./astro-app:/app
    working_dir: /app
    ports: 
      - 3000:4321
    command: sh -c "npm install && npm run dev"
